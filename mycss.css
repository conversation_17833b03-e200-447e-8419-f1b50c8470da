* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Microsoft YaHei", sans-serif;
}

.header-title {
    background-color: #f8f9fa;
    color: #00706B;
    /* text-indent: 50px; */
    bottom: 10px;
    padding: 15px 20px;
    font-size: 24px;
    font-weight: bold;
    letter-spacing: 1px;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 0;
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-title .header-left {
    display: flex;
    align-items: center;
}

.header-title .header-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-title .user-info {
    font-size: 14px;
    color: #666;
    font-weight: normal;
    padding: 4px 8px;
    background-color: rgba(0, 112, 107, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(0, 112, 107, 0.2);
}

.logout-btn {
    background: none;
    border: 1px solid #dc3545;
    color: #dc3545;
    padding: 3px 8px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logout-btn:hover {
    background-color: #dc3545;
    color: white;
    transform: translateY(-1px);
}

body {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    background-color: #f8f9fa;
}

.main-container {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.screen {
    height: 100%;
    overflow-y: auto;
    padding: 15px;
    border-right: 1px solid #ddd;
    background-color: white;
}

#nav-screen {
    width: 280px;
    background-color: #f5f5f5;
}

/* 移除不再需要的nav-header样式 */

#project-screen {
    width: 350px;
}

#review-screen {
    flex: 1;
}

h2 {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    color: #333;
}

h3 {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
    color: #2c3e50;
    font-size: 17px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.city-item {
    margin-bottom: 15px;
}

.city-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.city-name {
    padding: 8px 10px;
    background-color: #00706B;
    color: white;
    cursor: pointer;
    border-radius: 4px;
    font-weight: bold;
    flex: 1;
}

.city-stats {
    display: flex;
    margin-left: 10px;
}

.stat-badge {
    width: 30px;
    height: 20px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    margin-left: 5px;
    color: white;
}

.total-count {
    background-color: #7f8c8d;
}

.completed-count {
    background-color: #2ecc71;
}

.option-list {
    margin-top: 5px;
    margin-left: 15px;
    display: none;
}

.option-item {
    padding: 12px 16px;
    margin-bottom: 6px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    transition: all 0.2s ease;
}

.option-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background-color: #00706b;
    border-radius: 0 4px 4px 0;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.option-item:hover {
    border-color: #00706b;
}

.option-item.active {
    background-color: #f8f9fa;
    border-color: #00706b;
}

.option-item.active::before {
    opacity: 1;
}





.option-name {
    font-weight: 500;
    font-size: 14px;
    line-height: 1.2;
    margin-bottom: 2px;
}

.type-stats {
    display: flex;
    margin-top: 6px;
    margin-left: 0;
    flex-wrap: wrap;
    gap: 3px;
}

.count-badge {
    width: 24px;
    height: 18px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
    color: white;
    transition: all 0.2s ease;
}

.red-count {
    background-color: #e74c3c;
}

.yellow-count {
    background-color: #f1c40f;
}

.blue-count {
    background-color: #3498db;
}

.total-type-count {
    background-color: #7f8c8d;
}

.completed-type-count {
    background-color: #2ecc71;
}

/* 添加徽章点击样式 */
.count-badge {
    cursor: pointer;
}

.count-badge:hover {
    opacity: 0.8;
}

.count-badge:active {
    transform: scale(0.95);
}

.project-item {
    padding: 12px;
    margin-bottom: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #ddd;
    cursor: pointer;
    position: relative;
    padding-bottom: 30px;
    /* 为状态框预留空间 */
    padding-left: 20px;
    /* 为左边竖条预留空间 */
}

/* 左边竖条基础样式 */
.project-item {
    --left-bar-color: #ccc;
    /* 默认颜色，会被JS动态覆盖 */
}

.project-item::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 6px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    background-color: var(--left-bar-color);
}

.project-item:hover {
    background-color: #e3f2fd;
    border-color: #bbdefb;
}

.project-item.active {
    background-color: #bbdefb;
    border-color: #64b5f6;
}

.project-status {
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: absolute;
    right: 1px;
    bottom: 1px;
}

.status-pending-submit {
    background-color: #e74c3c;
    color: white;
}

.status-rejected {
    background-color: #f1c40f;
    color: black;
}

.status-pending {
    background-color: #3498db;
    color: white;
}

.project-name {
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.project-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
}

.review-item {
    padding: 10px;
    margin-bottom: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #ddd;
    position: relative;
}

.review-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.file-status {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.approved {
    background-color: #2ecc71;
    color: white;
}

.rejected {
    background-color: #f1c40f;
    color: black;
}

.pending {
    background-color: #3498db;
    color: white;
}

.pending-submit {
    background-color: #e74c3c;
    color: white;
}

.reason-box {
    margin-top: 10px;
    padding: 8px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #555;
    font-size: 14px;
}

.upload-btn {
    margin-top: 10px;
    padding: 5px 10px;
    background-color: #27ae60;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.upload-gp-btn {
    margin-top: 10px;
    padding: 5px 10px;
    background-color: #27ae60;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.download-btn {
    margin-top: 10px;
    padding: 5px 10px;
    background-color: #2980b9;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    margin-right: 5px;
}

.pending-btn {
    margin-top: 10px;
    padding: 5px 10px;
    background-color: #9b59b6;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    margin-right: 5px;
}

.hidden {
    display: none;
}

.no-selection {
    color: #999;
    font-style: italic;
    text-align: center;
    margin-top: 20px;
}

.search-container {
    margin-bottom: 15px;
    position: relative;
}

.search-input {
    width: 70%;
    padding: 8px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.search-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.search-clear {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 16px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: none; /* 默认隐藏，当有内容时显示为flex */
    align-items: center;
    justify-content: center;
}

.search-clear::before {
    content: "×";
    font-size: 18px;
    line-height: 1;
}

.search-clear:hover {
    color: #666;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #eee;
    flex-wrap: nowrap; /* 防止换行 */
    gap: 3px; /* 适当增加按钮间距 */
    font-size: 13px; /* 增大字体大小 */
    max-width: 100%;
    overflow-x: auto;
}

.page-btn {
    padding: 4px 8px; /* 增大按钮内边距 */
    min-width: 28px; /* 增大按钮最小宽度 */
    height: 28px; /* 增大按钮高度 */
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 3px;
    cursor: pointer;
    font-size: 13px; /* 增大字体大小 */
    transition: all 0.2s;
}

.page-btn:hover:not(.disabled) {
    background-color: #e0e0e0;
    border-color: #ccc;
}

.page-btn.active {
    background-color: #3498db;
    color: white;
    border-color: #2980b9;
    font-weight: bold;
}

.page-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-nav-btn {
    font-size: 16px;
    font-weight: bold;
}

.page-ellipsis {
    margin: 0 2px;
    color: #888;
    user-select: none;
    font-size: 13px; /* 增大字体大小 */
}

.page-jump {
    display: flex;
    align-items: center;
    margin-left: 5px;
    font-size: 13px; /* 增大字体大小 */
}

.page-jump input {
    width: 35px; /* 增大输入框宽度 */
    height: 28px; /* 增大输入框高度 */
    margin: 0 3px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px; /* 增大输入框字体大小 */
}

.page-jump button {
    padding: 4px 8px; /* 增大按钮内边距 */
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 3px;
    cursor: pointer;
    font-size: 13px; /* 增大按钮字体大小 */
}

.page-jump button:hover {
    background-color: #e0e0e0;
}

/* 用户详情侧边栏样式 - 默认折叠状态 */
#userinfo-screen {
    width: 0px; /* 折叠状态完全隐藏，节省最大空间 */
    min-width: 0px;
    overflow: hidden; /* 折叠状态下隐藏所有内容 */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border-left: none; /* 折叠状态下移除边框 */
    background: transparent; /* 折叠状态下移除背景 */
    padding: 0; /* 移除所有内边距 */
    margin: 0; /* 移除所有外边距 */
}

/* 展开状态 */
#userinfo-screen.expanded {
    width: 360px;
    min-width: 360px;
    overflow: visible; /* 展开状态显示所有内容 */
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%); /* 展开时恢复背景 */
    border-left: 1px solid #e0e0e0; /* 展开时恢复边框 */
    padding: 0; /* 保持无内边距 */
    margin: 0; /* 保持无外边距 */
}

/* 折叠按钮 - 位于顶部标题栏 */
.userinfo-toggle-btn.header-toggle {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #00706B, #4a9b96);
    border: 2px solid #ffffff;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 13px;
    z-index: 1500;
    box-shadow: 0 2px 8px rgba(0, 112, 107, 0.4);
    transition: all 0.3s ease;
    margin: 0 8px; /* 与其他按钮保持一致的间距 */
    flex-shrink: 0;
}

.userinfo-toggle-btn.header-toggle:hover {
    background: linear-gradient(135deg, #005a56, #3d8a85);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 112, 107, 0.6);
}

.userinfo-toggle-btn.header-toggle:active {
    transform: scale(0.95);
}

/* 默认折叠状态下的内容完全隐藏 */
#userinfo-screen .userinfo-content {
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    overflow: hidden;
    padding: 0; /* 折叠状态下移除内边距 */
    margin: 0; /* 折叠状态下移除外边距 */
    width: 0; /* 折叠状态下宽度为0 */
    height: 0; /* 折叠状态下高度为0 */
}

#userinfo-screen .userinfo-header {
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    padding: 0; /* 折叠状态下移除内边距 */
    margin: 0; /* 折叠状态下移除外边距 */
    background: transparent; /* 折叠状态下移除背景 */
    border-bottom: none; /* 折叠状态下移除边框 */
    width: 0; /* 折叠状态下宽度为0 */
    height: 0; /* 折叠状态下高度为0 */
}

/* 展开状态下的内容显示 */
#userinfo-screen.expanded .userinfo-content {
    opacity: 1;
    pointer-events: auto;
    overflow-y: auto; /* 展开状态下启用垂直滚动 */
    overflow-x: hidden; /* 隐藏水平滚动 */
    max-height: calc(100vh - 120px); /* 限制最大高度，确保滚动条出现 */
    padding: 0 15px 15px 15px; /* 展开状态下恢复内边距 */
    width: auto; /* 展开状态下恢复宽度 */
    height: auto; /* 展开状态下恢复高度 */
}

#userinfo-screen.expanded .userinfo-header {
    opacity: 1;
    pointer-events: auto;
    padding: 15px; /* 展开状态下恢复内边距 */
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); /* 展开状态下恢复背景 */
    /*border-bottom: 2px solid #e3f2fd; /* 展开状态下恢复边框 */
    position: sticky; /* 保持头部固定 */
    top: 0;
    z-index: 10;
    flex-shrink: 0; /* 防止头部被压缩 */
    width: auto; /* 展开状态下恢复宽度 */
    height: auto; /* 展开状态下恢复高度 */
}

/* 全局隐藏滚动条但保留滚动功能 */
* {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
}

*::-webkit-scrollbar {
    display: none; /* WebKit */
}

/* 确保所有可滚动区域都隐藏滚动条 */
.screen::-webkit-scrollbar,
#userinfo-screen::-webkit-scrollbar,
#userinfo-screen .userinfo-content::-webkit-scrollbar,
#userinfo-screen .user-details .card-body::-webkit-scrollbar,
.card-body::-webkit-scrollbar,
.timeline::-webkit-scrollbar,
body::-webkit-scrollbar,
html::-webkit-scrollbar {
    display: none;
}

/* 确保滚动功能仍然正常工作 */
.screen,
#userinfo-screen.expanded .userinfo-content,
#userinfo-screen .user-details .card-body,
.card-body {
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

/* 移动端适配 */
@media (max-width: 768px) {
    #userinfo-screen {
        width: 100% !important;
        min-width: auto !important;
        background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%) !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    #userinfo-screen.expanded {
        width: 100% !important;
        min-width: auto !important;
    }

    #userinfo-screen .userinfo-content,
    #userinfo-screen .userinfo-header {
        opacity: 1 !important;
        pointer-events: auto !important;
        width: auto !important; /* 移动端恢复宽度 */
        height: auto !important; /* 移动端恢复高度 */
    }

    #userinfo-screen .userinfo-content {
        padding: 0 15px 15px 15px !important;
        max-height: calc(100vh - 150px) !important;
        overflow-y: auto !important;
    }

    #userinfo-screen .userinfo-header {
        padding: 15px !important;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
        border-bottom: 2px solid #e3f2fd !important;
    }

    /* 移动端按钮样式 - 在顶部标题栏 */
    .userinfo-toggle-btn.header-toggle {
        width: 28px !important;
        height: 28px !important;
        font-size: 11px !important;
        margin: 0 6px !important;
        border-width: 1px !important;
        box-shadow: 0 1px 4px rgba(0, 112, 107, 0.3) !important;
    }

    .userinfo-toggle-btn.header-toggle:hover {
        transform: scale(1.05) !important;
    }
}

/* 移除重复的样式定义，使用上面的新样式 */

.file-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 10px;
}

.file-input-wrapper {
    position: relative;
    overflow: hidden;
    display: inline-block;
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 4px;
    background-color: #2ecc71;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
}

.notification.error {
    background-color: #e74c3c;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 10px;
    vertical-align: middle;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.file-uploading {
    opacity: 0.7;
    position: relative;
}

.file-uploading:after {
    content: "上传中...";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #2980b9;
    font-weight: bold;
}

.project-details {
    margin-top: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.detail-item {
    display: flex;
    margin-bottom: 10px;
    font-size: 14px;
}

.detail-label {
    font-weight: bold;
    min-width: 100px;
    color: #555;
}

.detail-value {
    flex: 1;
    color: #333;
}

.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-active {
    background-color: #2ecc71;
}

.status-completed {
    background-color: #3498db;
}

.file-preview {
    margin-top: 10px;
    padding: 10px;
    border: 1px dashed #ddd;
    border-radius: 4px;
    background-color: #fafafa;
    display: none;
}

.file-preview img {
    max-width: 100%;
    max-height: 200px;
    display: block;
    margin: 0 auto;
}

.file-preview .file-name {
    text-align: center;
    margin-top: 10px;
    font-size: 14px;
    color: #555;
}

.active-filter {
    box-shadow: 0 0 0 2px white !important;
    transform: scale(1.1);
}

/* Timeline styles */
.timeline {
    list-style: none;
    padding: 20px 0 0 0;
    position: relative;
}

.timeline:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #e9ecef;
    left: 20px;
    margin: 0;
}

.timeline-item {
    margin-bottom: 20px;
    position: relative;
    padding-left: 50px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-icon {
    position: absolute;
    left: 20px;
    top: 0;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #adb5bd;
    /* secondary */
    border: 3px solid #f8f9fa;
    transform: translateX(-50%);
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

.timeline-item.completed .timeline-icon {
    background: #198754;
    /* success */
}

.timeline-item.pending .timeline-icon {
    background: #0d6efd;
    /* primary */
}

.timeline-item.not-entered .timeline-icon {
    background: #6c757d;
    /* secondary - 数据未录入状态 */
}

.timeline-item.not-entered .timeline-content {
    background: #f8f9fa;
    border: 1px dashed #dee2e6;
    opacity: 0.8;
}

.timeline-content {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    padding: 15px;
    border-radius: 8px;
    position: relative;
}

.timeline-content h6 {
    font-weight: 600;
    margin-top: 0;
}

.timeline-time {
    font-size: 0.85rem;
    color: #6c757d;
}

.user-details dl {
    margin-bottom: 0;
}

.user-details dt {
    font-weight: 500;
    color: #495057;
    display: flex;
    align-items: center;
}

.user-details dd {
    color: #212529;
}

#add-project-btn {
    background-color: #2980b9;
    border-color: #2980b9;
}

.modal-header {
    background-color: #2980b9;
    color: white;
}

.modal-header .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* 移动端导航栏 */
.mobile-nav {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    border-top: 1px solid #e0e0e0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    padding: 8px 0;
}

/* 移动端状态指示器 */
.mobile-status-indicator {
    display: none;
    position: fixed;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    flex-direction: column;
    gap: 8px;
    z-index: 999;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #ddd;
    transition: background-color 0.3s ease;
}

.status-dot.active {
    background-color: #00706B;
}

.mobile-nav .nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    touch-action: manipulation;
}

.mobile-nav .nav-item i {
    font-size: 20px;
    margin-bottom: 4px;
    color: #666;
    transition: color 0.3s ease;
}

.mobile-nav .nav-item span {
    font-size: 12px;
    color: #666;
    transition: color 0.3s ease;
}

.mobile-nav .nav-item.active i,
.mobile-nav .nav-item.active span {
    color: #00706B;
}

.mobile-nav .nav-item:hover:not(.active) i,
.mobile-nav .nav-item:hover:not(.active) span {
    color: #3498db;
}

/* 移动端适配样式 */
@media (max-width: 768px) {
    /* 显示移动端导航 */
    .mobile-nav {
        display: flex;
    }
    /* 基础布局调整 */
    body {
        overflow: auto;
        height: auto;
        min-height: 100vh;
        padding-bottom: 70px; /* 为底部导航栏留出空间 */
    }

    .header-title {
        position: relative;
        padding: 12px 15px;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
    }

    .header-title .header-left {
        display: flex;
        align-items: center;
    }

    .header-title .header-right {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .header-title img {
        width: 35px !important;
        height: 35px !important;
        margin-top: 0 !important;
        margin-right: 8px !important;
    }

    .header-title .user-info {
        font-size: 12px;
        padding: 2px 6px;
        margin-top: 2px;
    }

    .logout-btn {
        padding: 4px 6px;
        font-size: 12px;
    }

    .main-container {
        flex-direction: column;
        height: auto;
        overflow: visible;
        position: relative;
    }

    .screen {
        width: 100% !important;
        height: auto;
        border-right: none;
        border-bottom: none;
        padding: 15px;
        overflow-y: visible;
        display: none; /* 默认隐藏所有屏幕 */
    }

    .screen.active {
        display: block; /* 只显示激活的屏幕 */
    }


    /* 标题样式调整 */
    h3 {
        font-size: 16px;
        margin-bottom: 12px;
        padding-bottom: 8px;
    }

    /* 城市导航优化 */
    .city-item {
        margin-bottom: 12px;
    }

    .city-name {
        padding: 12px 15px;
        font-size: 16px;
        border-radius: 6px;
        touch-action: manipulation;
    }

    .city-stats {
        margin-left: 8px;
    }

    .stat-badge {
        width: 35px;
        height: 25px;
        font-size: 13px;
        margin-left: 6px;
        border-radius: 4px;
    }

    .option-item {
        padding: 14px 18px;
        margin-bottom: 8px;
        border-radius: 8px;
        touch-action: manipulation;
    }

    .option-name {
        font-size: 15px;
        font-weight: 500;
        margin-bottom: 6px;
    }

    .count-badge {
        width: 28px;
        height: 22px;
        font-size: 11px;
        border-radius: 3px;
    }

    .total-type-count,
    .completed-type-count {
        width: 30px;
        height: 22px;
        font-size: 11px;
    }

    .type-stats {
        margin-top: 8px;
        gap: 4px;
    }

    /* 项目列表优化 */
    .project-item {
        padding: 15px;
        padding-left: 25px;
        margin-bottom: 12px;
        border-radius: 8px;
        touch-action: manipulation;
    }

    .project-item::before {
        width: 8px;
    }

    .project-name {
        font-size: 16px;
        margin-bottom: 8px;
        line-height: 1.4;
    }

    .project-meta {
        font-size: 13px;
        flex-direction: column;
        gap: 4px;
    }

    .project-status {
        position: static;
        margin-top: 8px;
        font-size: 13px;
        padding: 6px 12px;
        border-radius: 6px;
        display: inline-block;
    }

    /* 搜索框优化 */
    .search-input {
        padding: 12px 15px;
        font-size: 16px;
        border-radius: 8px;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    .search-clear {
        right: 15px;
        font-size: 18px;
        padding: 5px;
    }

    /* 按钮优化 */
    #add-project-btn {
        padding: 12px 20px;
        font-size: 16px;
        border-radius: 8px;
        margin-bottom: 15px;
        touch-action: manipulation;
    }

    /* 分页优化 */
    .pagination {
        flex-wrap: wrap;
        gap: 8px;
        font-size: 14px;
        justify-content: center;
        padding: 15px 0;
    }

    .page-btn {
        min-width: 40px;
        height: 40px;
        font-size: 14px;
        border-radius: 6px;
        touch-action: manipulation;
    }

    .page-jump input {
        width: 50px;
        height: 40px;
        font-size: 14px;
        border-radius: 6px;
    }

    .page-jump button {
        padding: 8px 12px;
        font-size: 14px;
        border-radius: 6px;
        touch-action: manipulation;
    }

    /* 审核状态优化 */
    .review-item {
        padding: 15px;
        margin-bottom: 15px;
        border-radius: 8px;
        touch-action: manipulation;
    }

    .review-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        margin-bottom: 12px;
    }

    .file-status {
        padding: 6px 12px;
        font-size: 13px;
        border-radius: 6px;
    }

    .reason-box {
        padding: 12px;
        font-size: 15px;
        border-radius: 8px;
        line-height: 1.5;
    }

    /* 文件操作按钮优化 */
    .file-actions {
        gap: 10px;
        margin-top: 15px;
    }

    .upload-btn,
    .upload-gp-btn,
    .download-btn,
    .pending-btn {
        padding: 12px 16px;
        font-size: 14px;
        border-radius: 6px;
        min-width: 80px;
        touch-action: manipulation;
    }

    /* 用户详情优化 */
    .project-details {
        padding: 15px;
        border-radius: 10px;
        margin-top: 15px;
    }

    .detail-item {
        flex-direction: column;
        margin-bottom: 12px;
        font-size: 15px;
    }

    .detail-label {
        min-width: auto;
        margin-bottom: 4px;
        font-size: 14px;
    }

    .detail-value {
        font-size: 15px;
        padding-left: 0;
    }

    /* 时间线优化 */
    .timeline {
        padding: 15px 0 0 0;
    }

    .timeline:before {
        left: 15px;
    }

    .timeline-item {
        padding-left: 40px;
        margin-bottom: 15px;
    }

    .timeline-icon {
        left: 15px;
        width: 20px;
        height: 20px;
        font-size: 10px;
    }

    .timeline-content {
        padding: 12px;
        border-radius: 8px;
    }

    .timeline-content h6 {
        font-size: 15px;
        margin-bottom: 8px;
    }

    .timeline-time {
        font-size: 13px;
    }

    /* 用户详情列表优化 */
    .user-details dt {
        font-size: 14px;
        margin-bottom: 4px;
    }

    .user-details dd {
        font-size: 15px;
        margin-bottom: 12px;
        padding-left: 0;
    }

    /* 通知优化 */
    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        padding: 12px 15px;
        font-size: 15px;
        border-radius: 8px;
        text-align: center;
    }

    /* 文件预览优化 */
    .file-preview {
        padding: 15px;
        border-radius: 8px;
        margin-top: 15px;
    }

    .file-preview img {
        max-height: 250px;
    }

    .file-preview .file-name {
        font-size: 15px;
        margin-top: 12px;
    }

    /* 无选择状态优化 */
    .no-selection {
        font-size: 15px;
        margin-top: 30px;
        padding: 20px;
        line-height: 1.5;
    }

    /* 模态框优化 */
    .modal-dialog {
        margin: 10px;
        max-width: calc(100vw - 20px);
    }

    .modal-header {
        padding: 15px;
    }

    .modal-body {
        padding: 15px;
    }

    .modal-footer {
        padding: 15px;
    }

    /* 表单元素优化 */
    .form-control {
        padding: 12px 15px;
        font-size: 16px;
        border-radius: 8px;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    .btn {
        padding: 12px 20px;
        font-size: 16px;
        border-radius: 8px;
        touch-action: manipulation;
    }

    /* 防止双击缩放 */
    * {
        touch-action: manipulation;
    }

    /* 滚动条优化 */
    ::-webkit-scrollbar {
        width: 6px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* 移动端特殊优化 */
    .mobile-nav .nav-item {
        position: relative;
        overflow: hidden;
    }

    .mobile-nav .nav-item::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background-color: rgba(0, 112, 107, 0.1);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.3s ease, height 0.3s ease;
    }

    .mobile-nav .nav-item:active::before {
        width: 100px;
        height: 100px;
    }

    /* 改善点击反馈 */
    .city-name:active,
    .option-item:active,
    .project-item:active,
    .review-item:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    /* 输入框焦点优化 */
    .search-input:focus,
    .form-control:focus {
        transform: scale(1.02);
        transition: transform 0.2s ease;
    }

    /* 按钮点击反馈 */
    .btn:active,
    .upload-btn:active,
    .download-btn:active,
    .pending-btn:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }

    /* 页面切换动画 */
    .screen {
        transition: opacity 0.3s ease-in-out;
    }

    .screen:not(.active) {
        opacity: 0;
        pointer-events: none;
    }

    .screen.active {
        opacity: 1;
        pointer-events: auto;
    }

    /* 加载状态优化 */
    .loading-container {
        padding: 15px !important;
        border-radius: 10px !important;
    }

    .loading-text {
        font-size: 16px !important;
    }

    /* 改善小屏幕上的可读性 */
    @media (max-width: 480px) {
        .header-title {
            font-size: 16px;
            padding: 10px 15px;
        }

        .header-title img {
            width: 30px !important;
            height: 30px !important;
        }

        .header-title .header-right {
            gap: 5px;
        }

        .header-title .user-info {
            font-size: 11px;
            margin-left: 5px;
            padding: 1px 4px;
        }

        .logout-btn {
            padding: 2px 8px;
            font-size: 11px;
        }

        h3 {
            font-size: 15px;
        }

        .project-name {
            font-size: 15px;
        }

        .city-name {
            font-size: 15px;
            padding: 10px 12px;
        }

        .option-item {
            padding: 12px 14px;
            margin-bottom: 6px;
            border-radius: 6px;
        }

        .option-name {
            font-size: 14px;
            margin-bottom: 5px;
        }

        .count-badge {
            width: 26px;
            height: 20px;
            font-size: 10px;
        }

        .total-type-count,
        .completed-type-count {
            width: 28px;
            height: 20px;
            font-size: 10px;
        }

        .mobile-nav .nav-item i {
            font-size: 18px;
        }

        .mobile-nav .nav-item span {
            font-size: 11px;
        }

        .notification {
            font-size: 14px;
            padding: 10px 12px;
        }
    }

    /* 移动端屏幕切换动画优化 */
    @media (max-width: 768px) {
        .screen {
            transform: translateX(0);
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
        }

        .screen:not(.active) {
            opacity: 0;
            transform: translateX(-20px);
            pointer-events: none;
        }

        .screen.active {
            opacity: 1;
            transform: translateX(0);
            pointer-events: auto;
        }

        /* 移动端导航栏激活状态增强 */
        .mobile-nav .nav-item.active {
            background-color: rgba(0, 112, 107, 0.1);
            border-radius: 8px;
            margin: 0 2px;
        }

        .mobile-nav .nav-item.active::after {
            content: '';
            position: absolute;
            top: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 3px;
            background-color: #00706B;
            border-radius: 0 0 3px 3px;
        }

        /* 移动端返回按钮样式（如果需要） */
        .mobile-back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 40px;
            height: 40px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            border-radius: 50%;
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1001;
            font-size: 18px;
            touch-action: manipulation;
        }

        .mobile-back-btn.show {
            display: flex;
        }

        /* 优化移动端长列表滚动 */
        .screen {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
        }
    }
}

/* 移动端PDF查看器样式 */
.pdf-viewer-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.pdf-viewer-toolbar {
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.pdf-viewer-content {
    flex: 1;
    position: relative;
    min-height: 500px;
}

.pdf-fallback {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (max-width: 768px) {
    .pdf-viewer-toolbar {
        padding: 8px 12px;
    }

    .pdf-viewer-toolbar .btn {
        font-size: 14px;
        padding: 8px 12px;
        flex: 1;
        min-width: 120px;
    }

    .pdf-viewer-content {
        min-height: 400px;
    }

    #pdf-iframe {
        height: 400px !important;
    }

    .modal-fullscreen-sm-down .modal-body {
        padding: 0 !important;
    }

    .modal-fullscreen-sm-down .pdf-viewer-content {
        min-height: calc(100vh - 120px);
    }

    .modal-fullscreen-sm-down #pdf-iframe {
        height: calc(100vh - 120px) !important;
    }
}

/* PDF查看器加载状态 */
.pdf-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;
}

.pdf-loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 移动端文件操作按钮增强 */
@media (max-width: 768px) {
    .download-btn {
        position: relative;
        overflow: hidden;
    }

    .download-btn::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.3s ease, height 0.3s ease;
    }

    .download-btn:active::after {
        width: 100px;
        height: 100px;
    }
}