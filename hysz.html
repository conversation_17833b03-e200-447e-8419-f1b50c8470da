<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>华宇收资管理系统</title>
    <link rel="stylesheet" href="./bootstrap-icons.css">
    <link rel="stylesheet" href="./mycss.css">
    <link href="./bootstrap.min.css" rel="stylesheet">
</head>

<body>
    <!-- 通知区域 -->
    <div class="notification" id="notification"></div>

    <!-- 标题栏 -->
    <div class="header-title">
        <div class="header-left">
            <img src="./logo.png" alt="logo" style="width: 50px; height: 50px; margin-top: -11px;margin-right: 8px;">
            华宇收资管理系统
        </div>
        <div class="header-right">
            <div class="user-info" id="user-info">
                <!-- 用户信息将通过JavaScript动态显示 -->
            </div>
            <!-- 用户详情侧边栏折叠按钮 - 方案1：顶部标题栏 -->
            <button class="userinfo-toggle-btn header-toggle" id="userinfo-toggle-btn" title="展开用户详情">
                <i class="bi bi-chevron-right"></i>
            </button>
            <button class="logout-btn" id="logout-btn" title="退出登录">
                <i class="bi bi-box-arrow-right"></i>
            </button>
        </div>
    </div>

    <!-- 移动端导航栏 -->
    <div class="mobile-nav" id="mobile-nav">
        <div class="nav-item active" data-screen="nav-screen">
            <i class="bi bi-geo-alt-fill"></i>
            <span>地市</span>
        </div>
        <div class="nav-item" data-screen="project-screen">
            <i class="bi bi-folder-fill"></i>
            <span>项目</span>
        </div>
        <div class="nav-item" data-screen="userinfo-screen">
            <i class="bi bi-person-fill"></i>
            <span>用户</span>
        </div>
        <div class="nav-item" data-screen="review-screen">
            <i class="bi bi-check-circle-fill"></i>
            <span>审核</span>
        </div>
    </div>

    <!-- 移动端状态指示器 -->
    <div class="mobile-status-indicator" id="mobile-status-indicator">
        <div class="status-dot active" data-screen="nav-screen"></div>
        <div class="status-dot" data-screen="project-screen"></div>
        <div class="status-dot" data-screen="userinfo-screen"></div>
        <div class="status-dot" data-screen="review-screen"></div>
    </div>

    <div class="main-container">
        <!-- 第一屏：导航栏 -->
        <div class="screen active" id="nav-screen">
            <h3>地市导航</h3>
            <div id="city-list">
                <!-- 地市列表将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 第二屏：项目列表 -->
        <div class="screen" id="project-screen">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <h3 id="project-list-title">项目列表</h3>
                
            </div>
            <div class="search-container">
                
                <input type="text" class="search-input" placeholder="搜索项目名称,编号,户号..." id="project-search">
                <button class="search-clear" id="clear-search"></button>
                <button id="add-project-btn" class="btn btn-primary d-none" style="margin-bottom: 1px;">
                    <i class="bi bi-plus-circle-fill"></i> 新增
                </button>
               
               
            </div>
            <div id="project-list">
                <div class="no-selection">请从左侧选择地市和项目类型</div>
            </div>
            <div id="pagination" class="pagination"></div>
        </div>

        <div class="screen userinfo-sidebar" id="userinfo-screen">
            <div class="userinfo-header">
                <h3 id="userinfo-list-title">用户详情</h3>
            </div>
            <div class="userinfo-content">
                <div class="search-container"></div>
                <div id="userinfo-list">
                    <div class="no-selection">请从左侧选择一个项目</div>
                </div>
                <!-- 移除用户信息分页元素 -->
            </div>
        </div>

        <!-- 第三屏：审核状态 -->
        <div class="screen" id="review-screen">
            <h3>审核状态</h3>
            <div id="review-list">
                <div class="no-selection">请从左侧选择一个项目</div>
            </div>
        </div>
    </div>

    <!-- 移动端PDF查看器模态框 -->
    <div class="modal fade" id="mobile-pdf-viewer" tabindex="-1" aria-labelledby="mobilePdfViewerLabel" aria-hidden="true">
        <div class="modal-dialog modal-fullscreen-sm-down modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="mobilePdfViewerLabel">文件查看</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    <div class="pdf-viewer-container">
                        <div class="pdf-viewer-toolbar">
                            <button class="btn btn-sm btn-outline-primary" id="pdf-download-btn">
                                <i class="bi bi-download"></i> 下载文件
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" id="pdf-open-new-tab">
                                <i class="bi bi-box-arrow-up-right"></i> 新标签页打开
                            </button>
                        </div>
                        <div class="pdf-viewer-content">
                            <iframe id="pdf-iframe" src="" width="100%" height="500px" style="border: none;"></iframe>
                            <div class="pdf-fallback" style="display: none;">
                                <div class="text-center p-4">
                                    <i class="bi bi-file-earmark-pdf" style="font-size: 3rem; color: #dc3545;"></i>
                                    <h5 class="mt-3">无法在浏览器中显示PDF</h5>
                                    <p class="text-muted">您的浏览器不支持内嵌PDF查看，请选择以下选项：</p>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-primary" id="pdf-download-fallback">
                                            <i class="bi bi-download"></i> 下载到设备
                                        </button>
                                        <button class="btn btn-outline-primary" id="pdf-open-fallback">
                                            <i class="bi bi-box-arrow-up-right"></i> 在新标签页打开
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="./bootstrap.bundle.min.js"></script>
    <!--<script src="./md5.js"></script>-->
    <script src="./myjs.js"></script>
</body>

</html>